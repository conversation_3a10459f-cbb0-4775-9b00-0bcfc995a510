{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@faker-js/faker": "^9.0.3", "@headlessui/react": "^2.1.9", "@hookform/resolvers": "^3.9.0", "@mui/material": "^6.1.3", "@mui/system": "^6.1.3", "@mui/x-charts": "^7.20.0", "@react-oauth/google": "^0.12.1", "@splidejs/react-splide": "^0.7.12", "@splidejs/splide": "^4.1.4", "@types/passport-google-oauth20": "^2.0.16", "antd": "^5.21.3", "axios": "^1.7.7", "chart.js": "^4.4.4", "dotenv": "^16.4.5", "flatpickr": "^4.6.13", "flowbite": "^2.5.2", "flowbite-react": "^0.10.2", "framer-motion": "^11.11.7", "jsvectormap": "^1.6.0", "lodash": "^4.17.21", "next": "14.2.5", "next-themes": "^0.3.0", "passport-google-oauth20": "^2.0.0", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.2.0", "react-confetti": "^6.1.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-icon": "^1.0.0", "react-icons": "^5.3.0", "react-icons-kit": "^2.0.0", "react-loading-indicators": "^1.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "react-scroll": "^1.9.0", "react-toastify": "^10.0.5", "redux": "^5.0.1", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@types/node": "^20.16.11", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@types/react-scroll": "^1.8.10", "eslint": "^8.57.1", "eslint-config-next": "14.2.5", "postcss": "^8.4.47", "react-splide": "link:@types/splidejs/react-splide", "tailwindcss": "^3.4.13", "typescript": "^5.6.3"}}