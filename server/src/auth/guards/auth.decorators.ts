import { ExecutionContext, createParamDecorator } from '@nestjs/common'
import { SetMetadata } from '@nestjs/common'
import { AccessTokenPayload } from '../auth.interfaces'

export const IS_PUBLIC_KEY = 'isPublic'
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true)

export const User = createParamDecorator(
  (_, ctx: ExecutionContext): AccessTokenPayload => {
    const request = ctx.switchToHttp().getRequest()
    return request.user
  },
)
