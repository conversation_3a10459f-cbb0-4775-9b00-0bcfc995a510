import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { AuthModule } from './auth/auth.module'
import { DatabaseModule } from './database/database.module'
import { UsersModule } from './users/users.module'
import 'dotenv/config'
import * as Joi from '@hapi/joi'
import { AccountModule } from './account/account.module'
import { BudgetModule } from './budget/budget.module'
import { EmailConfirmationModule } from './emailConfirmation/emailConfirmation.module'
import { GoogleAuthenticationModule } from './googleAuth/googleAuth.module'
import { PermissionsModule } from './permissions/permissions.module'
import { TransactionModule } from './transaction/transaction.module'

@Module({
  imports: [
    ConfigModule.forRoot({
      validationSchema: Joi.object({
        GOOGLE_AUTH_CLIENT_ID: Joi.string().required(),
        GOOGLE_AUTH_CLIENT_SECRET: Joi.string().required(),
        // ...
      }),
      isGlobal: true,
    }),
    AuthModule,
    UsersModule,
    EmailConfirmationModule,
    GoogleAuthenticationModule,
    PermissionsModule,
    TransactionModule,
    AccountModule,
    BudgetModule,
    DatabaseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        host: configService.get<string>('POSTGRES_HOST'),
        port: configService.get<number>('POSTGRES_PORT'),
        user: configService.get<string>('POSTGRES_USER'),
        password: configService.get<string>('POSTGRES_PASSWORD'),
        database: configService.get<string>('POSTGRES_DB'),
      }),
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
