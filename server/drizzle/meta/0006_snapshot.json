{"id": "b807b1ce-2e8d-4174-9748-127757ac0316", "prevId": "c34425aa-1b57-4e7d-9c44-82d7bf07a33d", "version": "7", "dialect": "postgresql", "tables": {"public.Admins": {"name": "Admins", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "pid": {"name": "pid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'SUPERADMIN'"}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "refreshToken": {"name": "refreshToken", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Admins_pid_unique": {"name": "Admins_pid_unique", "nullsNotDistinct": false, "columns": ["pid"]}, "Admins_email_unique": {"name": "Admins_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}, "public.Users": {"name": "Users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "pid": {"name": "pid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "passwordInit": {"name": "passwordInit", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "profilePicture": {"name": "profilePicture", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "refreshToken": {"name": "refreshToken", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "isEmailConfirmed": {"name": "isEmailConfirmed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isRegisteredWithGoogle": {"name": "isRegisteredWithGoogle", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "confirmationSentAt": {"name": "confirmationSentAt", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "passwordResetToken": {"name": "passwordResetToken", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "passwordResetTokenExpires": {"name": "passwordResetTokenExpires", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "passwordResetTokenUsed": {"name": "passwordResetTokenUsed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Users_pid_unique": {"name": "Users_pid_unique", "nullsNotDistinct": false, "columns": ["pid"]}, "Users_email_unique": {"name": "Users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "Users_phone_unique": {"name": "Users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}