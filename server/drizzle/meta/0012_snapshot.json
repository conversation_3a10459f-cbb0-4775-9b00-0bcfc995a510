{"id": "5aba4417-55b0-4b94-98c2-5a141525d1f2", "prevId": "dc626a7f-353e-4527-8a82-0665d4ef4b0c", "version": "7", "dialect": "postgresql", "tables": {"public.Accounts": {"name": "Accounts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "balance": {"name": "balance", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Accounts_user_id_Users_id_fk": {"name": "Accounts_user_id_Users_id_fk", "tableFrom": "Accounts", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Admins": {"name": "Admins", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "pid": {"name": "pid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'ADMIN'"}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "refreshToken": {"name": "refreshToken", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Admins_pid_unique": {"name": "Admins_pid_unique", "nullsNotDistinct": false, "columns": ["pid"]}, "Admins_email_unique": {"name": "Admins_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}, "public.Budgets": {"name": "Budgets", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Budgets_user_id_Users_id_fk": {"name": "Budgets_user_id_Users_id_fk", "tableFrom": "Budgets", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Transactions": {"name": "Transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Transactions_user_id_Users_id_fk": {"name": "Transactions_user_id_Users_id_fk", "tableFrom": "Transactions", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Users": {"name": "Users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "pid": {"name": "pid", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'USER'"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "passwordInit": {"name": "passwordInit", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "profilePicture": {"name": "profilePicture", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "refreshToken": {"name": "refreshToken", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "isEmailConfirmed": {"name": "isEmailConfirmed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isRegisteredWithGoogle": {"name": "isRegisteredWithGoogle", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "confirmationSentAt": {"name": "confirmationSentAt", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "passwordResetToken": {"name": "passwordResetToken", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "passwordResetTokenExpires": {"name": "passwordResetTokenExpires", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "passwordResetTokenUsed": {"name": "passwordResetTokenUsed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "failedLoginAttempts": {"name": "failed<PERSON><PERSON>in<PERSON><PERSON><PERSON>s", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "accountLockedUntil": {"name": "accountLockedUntil", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Users_pid_unique": {"name": "Users_pid_unique", "nullsNotDistinct": false, "columns": ["pid"]}, "Users_email_unique": {"name": "Users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "Users_phone_unique": {"name": "Users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}