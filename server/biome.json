{
  "$schema": "https://biomejs.dev/schemas/1.8.1/schema.json",
  "formatter": {
    "enabled": true,
    "formatWithErrors": false,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineEnding": "lf",
    "lineWidth": 80,
    "attributePosition": "auto"
  },
  "organizeImports": { "enabled": true },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": false,
      "complexity": {
        "noBannedTypes": "warn",
        "noUselessThisAlias": "error",
        "noUselessTypeConstraint": "error",
        "useArrowFunction": "off"
      },
      "correctness": {
        "noPrecisionLoss": "error",
        "noUnusedVariables": "warn",
        "useArrayLiterals": "off"
      },
      "style": {
        "noNamespace": "error",
        "useAsConstAssertion": "error",
        "useBlockStatements": "off"
      },
      "suspicious": {
        "noExplicitAny": "off",
        "noExtraNonNullAssertion": "error",{ create } from 'zustand
        "noMisleadingInstantiator": "error",
        "noUnsafeDeclarationMerging": "error"
      }
    },
    "ignore": ["**/.eslintrc.js"]
  },
  "javascript": {
    "formatter": {
      "jsxQuoteStyle": "double",
      "quoteProperties": "asNeeded",
      "trailingCommas": "all",
      "semicolons": "asNeeded",
      "arrowParentheses": "always",
      "bracketSpacing": true,
      "bracketSameLine": false,
      "quoteStyle": "single",
      "attributePosition": "auto"
    },
    "parser": {
      "unsafeParameterDecoratorsEnabled": true
    }
  },
  "overrides": [
    {
      "include": ["*.ts", "*.tsx", "*.mts", "*.cts"],
      "linter": {
        "rules": {
          "correctness": {
            "noConstAssign": "off",
            "noGlobalObjectCalls": "off",
            "noInvalidConstructorSuper": "off",
            "noNewSymbol": "off",
            "noSetterReturn": "off",
            "noUndeclaredVariables": "off",
            "noUnreachable": "off",
            "noUnreachableSuper": "off"
          },
          "style": {
            "noArguments": "error",
            "noVar": "error",
            "useConst": "error"
          },
          "suspicious": {
            "noExplicitAny": "off",
            "noExtraNonNullAssertion": "error",
            "noMisleadingInstantiator": "error",
            "noUnsafeDeclarationMerging": "error",
            
            "noDuplicateClassMembers": "off",
            "noDuplicateObjectKeys": "off",
            "noDuplicateParameters": "off",
            "noFunctionAssign": "off",
            "noImportAssign": "off",
            "noRedeclare": "off",
            "noUnsafeNegation": "off",
            "useGetterReturn": "off"
          }
        }
      }
    }
  ]
}
